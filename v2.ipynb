{
 "cells": [
  {
   "cell_type": "markdown",
   "id": "header",
   "metadata": {},
   "source": [
    "# Java Code Analysis Pipeline v2.0\n",
    "## Clean, Streamlined Data Lineage and Graph Analysis\n",
    "\n",
    "**Key Improvements:**\n",
    "- ✅ Removed duplicate functions\n",
    "- ✅ Single Neo4j push at the end\n",
    "- ✅ Consolidated similar functions\n",
    "- ✅ Clean execution flow\n",
    "- ✅ Enhanced data lineage with transformation chains"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "config-header",
   "metadata": {},
   "source": [
    "## 1. Configuration and Setup"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "config-cell",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ================== CONFIGURATION ==================\n",
    "import os\n",
    "import re\n",
    "import javalang\n",
    "import pandas as pd\n",
    "import json\n",
    "import hashlib\n",
    "from datetime import datetime\n",
    "from collections import defaultdict, deque\n",
    "from pathlib import Path\n",
    "from neo4j import GraphDatabase\n",
    "\n",
    "# Project Configuration\n",
    "PROJECT_PATH = \"OneInsights\"  # Update this to your Java project path\n",
    "CSV_OUTPUT_DIR = \"enhanced_graph_csv_v2\"\n",
    "\n",
    "# Neo4j Configuration\n",
    "NEO4J_URI = \"bolt://localhost:7687\"\n",
    "NEO4J_USER = \"neo4j\"\n",
    "NEO4J_PASSWORD = \"password\"  # Update with your Neo4j password\n",
    "NEO4J_DB = \"neo4j\"\n",
    "\n",
    "# SQL stopwords for filtering\n",
    "SQL_STOPWORDS = {\n",
    "    \"select\",\"from\",\"where\",\"and\",\"or\",\"not\",\"in\",\"on\",\"as\",\"by\",\"with\",\"into\",\"values\",\n",
    "    \"insert\",\"update\",\"delete\",\"create\",\"drop\",\"alter\",\"table\",\"index\",\"view\",\"database\",\n",
    "    \"schema\",\"column\",\"primary\",\"foreign\",\"key\",\"constraint\",\"unique\",\"null\",\"default\",\n",
    "    \"varchar\",\"char\",\"int\",\"integer\",\"bigint\",\"smallint\",\"decimal\",\"numeric\",\"float\",\n",
    "    \"double\",\"boolean\",\"date\",\"time\",\"timestamp\",\"text\",\"blob\",\"clob\",\"auto_increment\",\n",
    "    \"if\",\"then\",\"else\",\"when\",\"end\",\"case\",\"distinct\",\"limit\",\"offset\",\n",
    "    \"like\",\"not\",\"null\",\"is\",\"inner\",\"left\",\"right\",\"outer\",\"full\",\"cross\"\n",
    "}\n",
    "\n",
    "# Enhanced node types for comprehensive Java application coverage\n",
    "JAVA_NODE_TYPES = {\n",
    "    'structural': ['package', 'folder', 'file', 'class', 'interface', 'enum', 'annotation'],\n",
    "    'behavioral': ['method', 'constructor', 'lambda', 'operation', 'condition', 'loop'],\n",
    "    'data': ['variable', 'field', 'parameter', 'return_value', 'constant'],\n",
    "    'persistence': ['database', 'table', 'column', 'index', 'constraint', 'view', 'procedure', 'db_operation'],\n",
    "    'integration': ['api_endpoint', 'message_queue', 'cache', 'external_service'],\n",
    "    'configuration': ['property', 'profile', 'bean', 'component_scan'],\n",
    "    'security': ['role', 'permission', 'authentication', 'authorization'],\n",
    "    'monitoring': ['metric', 'log', 'trace', 'health_check']\n",
    "}\n",
    "\n",
    "RELATIONSHIP_TYPES = {\n",
    "    'structural': ['CONTAINS', 'DECLARES', 'EXTENDS', 'IMPLEMENTS', 'IMPORTS'],\n",
    "    'behavioral': ['CALLS', 'INVOKES', 'RETURNS', 'THROWS', 'HANDLES'],\n",
    "    'data_flow': ['READS', 'WRITES', 'TRANSFORMS', 'PRODUCES', 'CONSUMES', 'INPUT_TO', 'TRANSFORMS_VIA', 'ASSIGNS_TO', 'FLOWS_TO'],\n",
    "    'dependency': ['DEPENDS_ON', 'INJECTS', 'AUTOWIRES', 'CONFIGURES'],\n",
    "    'persistence': ['MAPS_TO', 'JOINS', 'REFERENCES', 'CASCADES'],\n",
    "    'integration': ['CALLS_API', 'PUBLISHES', 'SUBSCRIBES', 'CACHES'],\n",
    "    'security': ['SECURES', 'AUTHORIZES', 'AUTHENTICATES', 'VALIDATES']\n",
    "}\n",
    "\n",
    "print(\"✅ Configuration loaded successfully!\")\n",
    "print(f\"📦 Libraries: javalang, pandas {pd.__version__}, neo4j\")\n",
    "print(f\"📁 Project: {PROJECT_PATH}\")\n",
    "print(f\"💾 CSV Output: {CSV_OUTPUT_DIR}\")\n",
    "print(f\"🔗 Neo4j: {NEO4J_URI}\")\n",
    "print(f\"📊 Enhanced configuration:\")\n",
    "print(f\"   - SQL stopwords: {len(SQL_STOPWORDS)} terms\")\n",
    "print(f\"   - Java node types: {sum(len(v) for v in JAVA_NODE_TYPES.values())} types across {len(JAVA_NODE_TYPES)} categories\")\n",
    "print(f\"   - Relationship types: {sum(len(v) for v in RELATIONSHIP_TYPES.values())} types across {len(RELATIONSHIP_TYPES)} categories\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "core-classes-header",
   "metadata": {},
   "source": [
    "## 2. Core Classes and Helper Functions"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "core-classes",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ================== CORE HELPER FUNCTIONS ==================\n",
    "\n",
    "def register_node(nodes, raw_node, file_path):\n",
    "    \"\"\"Register a node with enhanced metadata.\"\"\"\n",
    "    if not raw_node or raw_node in nodes:\n",
    "        return\n",
    "    \n",
    "    node_type, full_name = raw_node.split(\":\", 1)\n",
    "    short_name = full_name.split(\".\")[-1] if \".\" in full_name else full_name\n",
    "    \n",
    "    nodes[raw_node] = {\n",
    "        \"id\": raw_node,\n",
    "        \"type\": node_type,\n",
    "        \"name\": short_name,\n",
    "        \"full_name\": full_name,\n",
    "        \"file_path\": file_path\n",
    "    }\n",
    "\n",
    "def add_relation(relations, existing_relations, src, rel, dst, file_path, nodes):\n",
    "    \"\"\"Add relation with duplicate detection and node registration.\"\"\"\n",
    "    if not src or not dst:\n",
    "        return\n",
    "    \n",
    "    key = (src, rel, dst)\n",
    "    if key in existing_relations:\n",
    "        return\n",
    "    \n",
    "    # Register nodes\n",
    "    register_node(nodes, src, file_path)\n",
    "    register_node(nodes, dst, file_path)\n",
    "    \n",
    "    relations.append({\n",
    "        \"source\": src,\n",
    "        \"relation\": rel,\n",
    "        \"target\": dst,\n",
    "        \"file_path\": file_path\n",
    "    })\n",
    "    existing_relations.add(key)\n",
    "\n",
    "print(\"✅ Core helper functions loaded\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "endpoint-tracker-header",\n",
   "metadata": {},
   "source": [
    "## 3. Endpoint and Usage Tracking"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "endpoint-tracker",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ================== ENDPOINT TRACKING ==================\n",
    "\n",
    "class EndpointUsageTracker:\n",
    "    \"\"\"Track API endpoints, their definitions, and usage patterns.\"\"\"\n",
    "    \n",
    "    def __init__(self):\n",
    "        self.endpoints = {}  # endpoint_path -> metadata\n",
    "        self.endpoint_methods = {}  # method_id -> endpoint info\n",
    "        self.method_calls = defaultdict(list)  # method -> [called_methods]\n",
    "        self.data_operations = defaultdict(list)  # method -> [operations]\n",
    "    \n",
    "    def register_endpoint(self, path, method, http_method, class_name, method_name, file_path):\n",
    "        \"\"\"Register an API endpoint with its definition location.\"\"\"\n",
    "        endpoint_key = f\"{http_method}:{path}\"\n",
    "        method_id = f\"method:{class_name}.{method_name}\"\n",
    "        \n",
    "        if endpoint_key not in self.endpoints:\n",
    "            self.endpoints[endpoint_key] = {\n",
    "                'path': path,\n",
    "                'http_method': http_method,\n",
    "                'class_name': class_name,\n",
    "                'method_name': method_name,\n",
    "                'file_path': file_path,\n",
    "                'method_id': method_id\n",
    "            }\n",
    "        \n",
    "        self.endpoint_methods[method_id] = endpoint_key\n",
    "        return endpoint_key\n",
    "    \n",
    "    def add_method_call(self, caller_method, called_method, operation_type='call'):\n",
    "        \"\"\"Track method calls and their operation types.\"\"\"\n",
    "        self.method_calls[caller_method].append({\n",
    "            'called_method': called_method,\n",
    "            'operation_type': operation_type\n",
    "        })\n",
    "    \n",
    "    def add_data_operation(self, method_id, operation_type, target, details=None):\n",
    "        \"\"\"Track data operations performed by methods.\"\"\"\n",
    "        self.data_operations[method_id].append({\n",
    "            'operation_type': operation_type,\n",
    "            'target': target,\n",
    "            'details': details or {}\n",
    "        })\n",
    "    \n",
    "    def get_endpoint_usage(self, endpoint_key):\n",
    "        \"\"\"Get detailed usage information for an endpoint.\"\"\"\n",
    "        if endpoint_key not in self.endpoints:\n",
    "            return None\n",
    "        \n",
    "        endpoint_info = self.endpoints[endpoint_key]\n",
    "        method_id = endpoint_info['method_id']\n",
    "        \n",
    "        return {\n",
    "            'endpoint': endpoint_info,\n",
    "            'method_calls': self.method_calls.get(method_id, []),\n",
    "            'data_operations': self.data_operations.get(method_id, [])\n",
    "        }\n",
    "\n",
    "print(\"✅ Endpoint tracking system loaded\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "extraction-functions-header",
   "metadata": {},
   "source": [
    "## 4. Extraction Functions"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "extraction-functions",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ================== EXTRACTION FUNCTIONS ==================\n",
    "\n",
    "def extract_spring_annotations(code):\n",
    "    \"\"\"Extract Spring framework annotations and their configurations.\"\"\"\n",
    "    annotations = []\n",
    "    patterns = {\n",
    "        'controller': r'@(?:RestController|Controller)(?:\\([^)]*\\))?',\n",
    "        'service': r'@Service(?:\\([^)]*\\))?',\n",
    "        'repository': r'@Repository(?:\\([^)]*\\))?',\n",
    "        'component': r'@Component(?:\\([^)]*\\))?',\n",
    "        'entity': r'@Entity(?:\\([^)]*\\))?',\n",
    "        'table': r'@Table\\([^)]*\\)',\n",
    "        'mapping': r'@(?:RequestMapping|GetMapping|PostMapping|PutMapping|DeleteMapping|PatchMapping)\\([^)]*\\)'\n",
    "    }\n",
    "    \n",
    "    for annotation_type, pattern in patterns.items():\n",
    "        try:\n",
    "            matches = re.finditer(pattern, code, re.IGNORECASE | re.MULTILINE)\n",
    "            for match in matches:\n",
    "                annotations.append({\n",
    "                    'type': annotation_type,\n",
    "                    'text': match.group(0),\n",
    "                    'start': match.start(),\n",
    "                    'end': match.end()\n",
    "                })\n",
    "        except re.error:\n",
    "            continue\n",
    "    \n",
    "    return annotations\n",
    "\n",
    "def extract_api_endpoints(code):\n",
    "    \"\"\"Enhanced API endpoint extraction with HTTP methods and parameters.\"\"\"\n",
    "    endpoints = []\n",
    "    \n",
    "    # Spring REST mappings with HTTP methods\n",
    "    mapping_patterns = {\n",
    "        'GET': r'@GetMapping\\s*\\(\\s*(?:value\\s*=\\s*)?[\"\\']([^\"\\']*)[\"\\'\\s]*\\)',\n",
    "        'POST': r'@PostMapping\\s*\\(\\s*(?:value\\s*=\\s*)?[\"\\']([^\"\\']*)[\"\\'\\s]*\\)',\n",
    "        'PUT': r'@PutMapping\\s*\\(\\s*(?:value\\s*=\\s*)?[\"\\']([^\"\\']*)[\"\\'\\s]*\\)',\n",
    "        'DELETE': r'@DeleteMapping\\s*\\(\\s*(?:value\\s*=\\s*)?[\"\\']([^\"\\']*)[\"\\'\\s]*\\)',\n",
    "        'PATCH': r'@PatchMapping\\s*\\(\\s*(?:value\\s*=\\s*)?[\"\\']([^\"\\']*)[\"\\'\\s]*\\)',\n",
    "        'REQUEST': r'@RequestMapping\\s*\\([^)]*value\\s*=\\s*[\"\\']([^\"\\']*)[\"\\'\\s]*[^)]*\\)'\n",
    "    }\n",
    "    \n",
    "    for http_method, pattern in mapping_patterns.items():\n",
    "        try:\n",
    "            matches = re.finditer(pattern, code, re.IGNORECASE | re.MULTILINE)\n",
    "            for match in matches:\n",
    "                path = match.group(1) if len(match.groups()) > 0 else \"/\"\n",
    "                endpoints.append({\n",
    "                    'http_method': http_method,\n",
    "                    'path': path,\n",
    "                    'annotation': match.group(0),\n",
    "                    'start': match.start(),\n",
    "                    'end': match.end()\n",
    "                })\n",
    "        except re.error:\n",
    "            continue\n",
    "    \n",
    "    return endpoints\n",
    "\n",
    "def extract_database_operations(code):\n",
    "    \"\"\"Extract database operations and table usage.\"\"\"\n",
    "    operations = {\n",
    "        'reads': set(),\n",
    "        'writes': set(),\n",
    "        'deletes': set(),\n",
    "        'tables': set()\n",
    "    }\n",
    "    \n",
    "    # Database operation patterns\n",
    "    patterns = {\n",
    "        'select': r'(?i)(?:SELECT|select)\\s+.*?\\s+(?:FROM|from)\\s+([a-zA-Z_][a-zA-Z0-9_]*)',\n",
    "        'insert': r'(?i)(?:INSERT|insert)\\s+(?:INTO|into)\\s+([a-zA-Z_][a-zA-Z0-9_]*)',\n",
    "        'update': r'(?i)(?:UPDATE|update)\\s+([a-zA-Z_][a-zA-Z0-9_]*)',\n",
    "        'delete': r'(?i)(?:DELETE|delete)\\s+(?:FROM|from)\\s+([a-zA-Z_][a-zA-Z0-9_]*)',\n",
    "        'jpa_find': r'find(?:By|All)([A-Z][a-zA-Z0-9]*)',\n",
    "        'jpa_save': r'save\\s*\\(',\n",
    "        'jpa_delete': r'delete(?:By|All)?([A-Z][a-zA-Z0-9]*)?'\n",
    "    }\n",
    "    \n",
    "    for operation_type, pattern in patterns.items():\n",
    "        try:\n",
    "            matches = re.finditer(pattern, code)\n",
    "            for match in matches:\n",
    "                if operation_type in ['select', 'jpa_find']:\n",
    "                    if len(match.groups()) > 0 and match.group(1):\n",
    "                        table_name = match.group(1)\n",
    "                        operations['reads'].add(table_name)\n",
    "                        operations['tables'].add(table_name)\n",
    "                elif operation_type in ['insert', 'update', 'jpa_save']:\n",
    "                    if len(match.groups()) > 0 and match.group(1):\n",
    "                        table_name = match.group(1)\n",
    "                        operations['writes'].add(table_name)\n",
    "                        operations['tables'].add(table_name)\n",
    "                elif operation_type in ['delete', 'jpa_delete']:\n",
    "                    if len(match.groups()) > 0 and match.group(1):\n",
    "                        table_name = match.group(1)\n",
    "                        operations['deletes'].add(table_name)\n",
    "                        operations['tables'].add(table_name)\n",
    "        except re.error:\n",
    "            continue\n",
    "    \n",
    "    return operations\n",
    "\n",
    "def extract_database_and_tables(code, file_path, nodes, relations, existing_relations):\n",
    "    \"\"\"Extract database connections and table references from Java code.\"\"\"\n",
    "    \n",
    "    databases = set()\n",
    "    tables = set()\n",
    "    rel_path = os.path.relpath(file_path, start='.')\n",
    "    \n",
    "    # Database connection patterns\n",
    "    db_patterns = {\n",
    "        'mongodb': [\n",
    "            r'MongoClient\\s*\\(',\n",
    "            r'@Document\\s*\\(',\n",
    "            r'MongoTemplate',\n",
    "            r'MongoRepository'\n",
    "        ],\n",
    "        'mysql': [\n",
    "            r'jdbc:mysql://([^/]+)',\n",
    "            r'MySQL',\n",
    "            r'com\\.mysql'\n",
    "        ],\n",
    "        'postgresql': [\n",
    "            r'jdbc:postgresql://([^/]+)',\n",
    "            r'PostgreSQL',\n",
    "            r'org\\.postgresql'\n",
    "        ],\n",
    "        'oracle': [\n",
    "            r'jdbc:oracle:thin:@([^:]+)',\n",
    "            r'Oracle',\n",
    "            r'oracle\\.jdbc'\n",
    "        ],\n",
    "        'h2': [\n",
    "            r'jdbc:h2:([^;]+)',\n",
    "            r'H2Database',\n",
    "            r'org\\.h2'\n",
    "        ]\n",
    "    }\n",
    "    \n",
    "    # Table/Collection patterns\n",
    "    table_patterns = [\n",
    "        r'@Table\\s*\\(\\s*name\\s*=\\s*[\"\\']([^\"\\']*)[\"\\'\\s]*\\)',\n",
    "        r'@Document\\s*\\(\\s*collection\\s*=\\s*[\"\\']([^\"\\']*)[\"\\'\\s]*\\)',\n",
    "        r'FROM\\s+([A-Za-z_][A-Za-z0-9_]*)',\n",
    "        r'INSERT\\s+INTO\\s+([A-Za-z_][A-Za-z0-9_]*)',\n",
    "        r'UPDATE\\s+([A-Za-z_][A-Za-z0-9_]*)',\n",
    "        r'DELETE\\s+FROM\\s+([A-Za-z_][A-Za-z0-9_]*)',\n",
    "        r'CREATE\\s+TABLE\\s+([A-Za-z_][A-Za-z0-9_]*)',\n",
    "        r'DROP\\s+TABLE\\s+([A-Za-z_][A-Za-z0-9_]*)'\n",
    "    ]\n",
    "    \n",
    "    # Extract databases\n",
    "    for db_type, patterns in db_patterns.items():\n",
    "        for pattern in patterns:\n",
    "            try:\n",
    "                matches = re.finditer(pattern, code, re.IGNORECASE)\n",
    "                for match in matches:\n",
    "                    if match.groups():\n",
    "                        db_name = match.group(1)\n",
    "                    else:\n",
    "                        db_name = db_type\n",
    "                    \n",
    "                    databases.add((db_type, db_name))\n",
    "                    \n",
    "                    # Create database node\n",
    "                    db_id = f\"database:{db_type}_{db_name}\"\n",
    "                    if db_id not in nodes:\n",
    "                        nodes[db_id] = {\n",
    "                            'id': db_id,\n",
    "                            'type': 'database',\n",
    "                            'name': db_name,\n",
    "                            'full_name': f\"{db_type}:{db_name}\",\n",
    "                            'file_path': rel_path,\n",
    "                            'db_type': db_type,\n",
    "                            'description': f'{db_type.upper()} database'\n",
    "                        }\n",
    "            except re.error:\n",
    "                continue\n",
    "    \n",
    "    # Extract tables/collections\n",
    "    for pattern in table_patterns:\n",
    "        try:\n",
    "            matches = re.finditer(pattern, code, re.IGNORECASE)\n",
    "            for match in matches:\n",
    "                if match.groups():\n",
    "                    table_name = match.group(1)\n",
    "                    if table_name and len(table_name) > 1 and table_name.lower() not in SQL_STOPWORDS:\n",
    "                        tables.add(table_name)\n",
    "                        \n",
    "                        # Create table node\n",
    "                        table_id = f\"table:{table_name}\"\n",
    "                        if table_id not in nodes:\n",
    "                            nodes[table_id] = {\n",
    "                                'id': table_id,\n",
    "                                'type': 'table',\n",
    "                                'name': table_name,\n",
    "                                'full_name': table_name,\n",
    "                                'file_path': rel_path,\n",
    "                                'description': 'Database table/collection'\n",
    "                            }\n",
    "                        \n",
    "                        # Connect tables to databases if found\n",
    "                        for db_type, db_name in databases:\n",
    "                            db_id = f\"database:{db_type}_{db_name}\"\n",
    "                            add_relation(relations, existing_relations, db_id, \"CONTAINS\", table_id, rel_path, nodes)\n",
    "        except re.error:\n",
    "            continue\n",
    "    \n",
    "    return databases, tables\n",
    "\n",
    "def analyze_database_usage(code, file_path, nodes, relations, existing_relations):\n",
    "    \"\"\"Analyze database operations and CRUD patterns.\"\"\"\n",
    "    \n",
    "    rel_path = os.path.relpath(file_path, start='.')\n",
    "    operations = []\n",
    "    \n",
    "    # CRUD operation patterns\n",
    "    crud_patterns = {\n",
    "        'CREATE': [\n",
    "            r'save\\s*\\(',\n",
    "            r'insert\\s*\\(',\n",
    "            r'persist\\s*\\(',\n",
    "            r'INSERT\\s+INTO'\n",
    "        ],\n",
    "        'READ': [\n",
    "            r'find\\s*\\(',\n",
    "            r'findBy([A-Za-z]+)',\n",
    "            r'findAll\\s*\\(',\n",
    "            r'SELECT\\s+.*\\s+FROM'\n",
    "        ],\n",
    "        'UPDATE': [\n",
    "            r'update\\s*\\(',\n",
    "            r'merge\\s*\\(',\n",
    "            r'UPDATE\\s+.*\\s+SET'\n",
    "        ],\n",
    "        'DELETE': [\n",
    "            r'delete\\s*\\(',\n",
    "            r'remove\\s*\\(',\n",
    "            r'DELETE\\s+FROM'\n",
    "        ]\n",
    "    }\n",
    "    \n",
    "    for operation_type, patterns in crud_patterns.items():\n",
    "        for pattern in patterns:\n",
    "            try:\n",
    "                matches = re.finditer(pattern, code, re.IGNORECASE)\n",
    "                for match in matches:\n",
    "                    operation_id = f\"db_operation:{operation_type}_{len(operations)}\"\n",
    "                    \n",
    "                    # Create operation node\n",
    "                    if operation_id not in nodes:\n",
    "                        nodes[operation_id] = {\n",
    "                            'id': operation_id,\n",
    "                            'type': 'db_operation',\n",
    "                            'name': f\"{operation_type}_{len(operations)}\",\n",
    "                            'full_name': operation_id,\n",
    "                            'file_path': rel_path,\n",
    "                            'operation_type': operation_type,\n",
    "                            'description': f'Database {operation_type} operation'\n",
    "                        }\n",
    "                    \n",
    "                    operations.append({\n",
    "                        'id': operation_id,\n",
    "                        'type': operation_type,\n",
    "                        'pattern': pattern,\n",
    "                        'match': match.group(0)\n",
    "                    })\n",
    "            except re.error:\n",
    "                continue\n",
    "    \n",
    "    return operations\n",
    "\n",
    "print(\"✅ Enhanced extraction functions loaded\")\n",
    "print(\"📊 New capabilities:\")\n",
    "print(\"   - Database detection (MongoDB, MySQL, PostgreSQL, Oracle, H2)\")\n",
    "print(\"   - Enhanced CRUD operation analysis\")\n",
    "print(\"   - Table/collection relationship mapping\")\n",
    "print(\"   - JPA annotation processing\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "transformation-header",
   "metadata": {},
   "source": [
    "## 5. Variable Transformation and Control Flow"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "transformation-functions",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ================== VARIABLE TRANSFORMATION AND CONTROL FLOW ==================\n",
    "\n",
    "def extract_variable_transformations(method_body, full_decl_name, method_name, relations, existing_relations, nodes, variable_flows, method_assignments, rel_path):\n",
    "    \"\"\"Extract variable transformation chains with operations as intermediate nodes.\"\"\"\n",
    "    method_node = f\"method:{full_decl_name}.{method_name}\"\n",
    "    \n",
    "    for path, node in method_body:\n",
    "        # Variable assignments with operation tracking\n",
    "        if hasattr(node, 'type') and node.type == 'LocalVariableDeclaration':\n",
    "            for declarator in node.declarators:\n",
    "                if hasattr(declarator, 'initializer') and declarator.initializer:\n",
    "                    var_name = declarator.name\n",
    "                    var_node = f\"variable:{full_decl_name}.{method_name}.{var_name}\"\n",
    "                    \n",
    "                    # Process method chain operations (e.g., x = y.trim().toUpperCase())\n",
    "                    if hasattr(declarator.initializer, 'member'):\n",
    "                        current_expr = declarator.initializer\n",
    "                        chain_vars = []\n",
    "                        operations = []\n",
    "                        \n",
    "                        # Extract the chain\n",
    "                        while hasattr(current_expr, 'member'):\n",
    "                            operations.append(current_expr.member)\n",
    "                            if hasattr(current_expr, 'qualifier'):\n",
    "                                current_expr = current_expr.qualifier\n",
    "                            else:\n",
    "                                break\n",
    "                        \n",
    "                        # Get the source variable\n",
    "                        if hasattr(current_expr, 'member'):\n",
    "                            source_var = current_expr.member\n",
    "                        elif hasattr(current_expr, 'name'):\n",
    "                            source_var = current_expr.name\n",
    "                        else:\n",
    "                            source_var = str(current_expr)\n",
    "                        \n",
    "                        # Create transformation chain: source -> Operation -> intermediate -> Operation -> target\n",
    "                        if operations and source_var:\n",
    "                            source_var_node = f\"variable:{full_decl_name}.{method_name}.{source_var}\"\n",
    "                            current_var_node = source_var_node\n",
    "                            \n",
    "                            # Reverse operations to get correct order\n",
    "                            operations.reverse()\n",
    "                            \n",
    "                            for i, operation in enumerate(operations):\n",
    "                                # Create operation node\n",
    "                                operation_node = f\"operation:{full_decl_name}.{method_name}.{operation}_{i}\"\n",
    "                                register_node(nodes, operation_node, rel_path)\n",
    "                                \n",
    "                                # Connect current variable to operation\n",
    "                                add_relation(relations, existing_relations, current_var_node, \"TRANSFORMS_TO\", operation_node, rel_path, nodes)\n",
    "                                \n",
    "                                # Create intermediate variable or final variable\n",
    "                                if i < len(operations) - 1:\n",
    "                                    intermediate_var = f\"variable:{full_decl_name}.{method_name}.{source_var}_{operation}_{i}\"\n",
    "                                    add_relation(relations, existing_relations, operation_node, \"PRODUCES\", intermediate_var, rel_path, nodes)\n",
    "                                    current_var_node = intermediate_var\n",
    "                                else:\n",
    "                                    # Final transformation to target variable\n",
    "                                    add_relation(relations, existing_relations, operation_node, \"PRODUCES\", var_node, rel_path, nodes)\n",
    "                            \n",
    "                            # Track the complete flow\n",
    "                            variable_flows.append({\n",
    "                                'method': method_node,\n",
    "                                'source': source_var,\n",
    "                                'target': var_name,\n",
    "                                'operations': operations,\n",
    "                                'type': 'method_chain'\n",
    "                            })\n",
    "                    \n",
    "                    # Simple assignment (x = y)\n",
    "                    elif hasattr(declarator.initializer, 'member'):\n",
    "                        source_var = declarator.initializer.member\n",
    "                        source_var_node = f\"variable:{full_decl_name}.{method_name}.{source_var}\"\n",
    "                        add_relation(relations, existing_relations, source_var_node, \"ASSIGNED_TO\", var_node, rel_path, nodes)\n",
    "                        \n",
    "                        variable_flows.append({\n",
    "                            'method': method_node,\n",
    "                            'source': source_var,\n",
    "                            'target': var_name,\n",
    "                            'operations': [],\n",
    "                            'type': 'simple_assignment'\n",
    "                        })\n",
    "\n",
    "def extract_control_flow_structures(method_body, full_decl_name, method_name, relations, existing_relations, nodes, variable_flows, method_assignments, rel_path):\n",
    "    \"\"\"Extract control flow structures as generic CONDITION and LOOP nodes.\"\"\"\n",
    "    method_node = f\"method:{full_decl_name}.{method_name}\"\n",
    "    \n",
    "    condition_count = 0\n",
    "    loop_count = 0\n",
    "    \n",
    "    for path, node in method_body:\n",
    "        # If statements\n",
    "        if hasattr(node, 'type') and node.type == 'IfStatement':\n",
    "            condition_count += 1\n",
    "            condition_id = f\"condition:{full_decl_name}.{method_name}.if_{condition_count}\"\n",
    "            \n",
    "            # Register condition node\n",
    "            register_node(nodes, condition_id, rel_path)\n",
    "            nodes[condition_id]['condition_type'] = 'if'\n",
    "            nodes[condition_id]['condition_text'] = str(node.condition) if hasattr(node, 'condition') else 'unknown'\n",
    "            \n",
    "            # Connect method to condition\n",
    "            add_relation(relations, existing_relations, method_node, \"HAS_CONDITION\", condition_id, rel_path, nodes)\n",
    "            \n",
    "            # Extract variables from condition\n",
    "            if hasattr(node, 'condition'):\n",
    "                condition_vars = extract_variables_from_expression(node.condition, full_decl_name, method_name)\n",
    "                for var in condition_vars:\n",
    "                    var_node = f\"variable:{full_decl_name}.{method_name}.{var}\"\n",
    "                    add_relation(relations, existing_relations, var_node, \"USED_IN_CONDITION\", condition_id, rel_path, nodes)\n",
    "        \n",
    "        # For loops\n",
    "        elif hasattr(node, 'type') and node.type == 'ForStatement':\n",
    "            loop_count += 1\n",
    "            loop_id = f\"loop:{full_decl_name}.{method_name}.for_{loop_count}\"\n",
    "            \n",
    "            # Register loop node\n",
    "            register_node(nodes, loop_id, rel_path)\n",
    "            nodes[loop_id]['loop_type'] = 'for'\n",
    "            nodes[loop_id]['condition_text'] = str(node.condition) if hasattr(node, 'condition') else 'unknown'\n",
    "            \n",
    "            # Connect method to loop\n",
    "            add_relation(relations, existing_relations, method_node, \"HAS_LOOP\", loop_id, rel_path, nodes)\n",
    "        \n",
    "        # While loops\n",
    "        elif hasattr(node, 'type') and node.type == 'WhileStatement':\n",
    "            loop_count += 1\n",
    "            loop_id = f\"loop:{full_decl_name}.{method_name}.while_{loop_count}\"\n",
    "            \n",
    "            # Register loop node\n",
    "            register_node(nodes, loop_id, rel_path)\n",
    "            nodes[loop_id]['loop_type'] = 'while'\n",
    "            nodes[loop_id]['condition_text'] = str(node.condition) if hasattr(node, 'condition') else 'unknown'\n",
    "            \n",
    "            # Connect method to loop\n",
    "            add_relation(relations, existing_relations, method_node, \"HAS_LOOP\", loop_id, rel_path, nodes)\n",
    "    \n",
    "    return condition_count + loop_count\n",
    "\n",
    "def extract_variables_from_expression(expression, full_decl_name, method_name):\n",
    "    \"\"\"Extract variable names from an expression.\"\"\"\n",
    "    variables = []\n",
    "    \n",
    "    if hasattr(expression, 'member'):\n",
    "        variables.append(expression.member)\n",
    "    elif hasattr(expression, 'name'):\n",
    "        variables.append(expression.name)\n",
    "    \n",
    "    # Recursively extract from sub-expressions\n",
    "    if hasattr(expression, 'qualifier'):\n",
    "        variables.extend(extract_variables_from_expression(expression.qualifier, full_decl_name, method_name))\n",
    "    \n",
    "    return variables\n",
    "\n",
    "print(\"✅ Variable transformation and control flow functions loaded\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "main-extraction-header",
   "metadata": {},
   "source": [
    "## 6. Main Extraction Function"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "main-extraction",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ================== MAIN EXTRACTION FUNCTION ==================\n",
    "\n",
    "def extract_complete_java_analysis(project_path):\n",
    "    \"\"\"Complete Java code analysis with all features - single unified function.\"\"\"\n",
    "    print(f\"🚀 Starting complete Java analysis for: {project_path}\")\n",
    "    \n",
    "    # Initialize data structures\n",
    "    nodes = {}\n",
    "    relations = []\n",
    "    existing_relations = set()\n",
    "    endpoint_tracker = EndpointUsageTracker()\n",
    "    variable_flows = []\n",
    "    method_assignments = defaultdict(list)\n",
    "    databases = set()\n",
    "    tables = set()\n",
    "    \n",
    "    # Statistics\n",
    "    stats = {\n",
    "        'files_processed': 0,\n",
    "        'classes_found': 0,\n",
    "        'methods_found': 0,\n",
    "        'endpoints_found': 0,\n",
    "        'control_flow_structures': 0,\n",
    "        'variable_transformations': 0,\n",
    "        'database_operations': 0\n",
    "    }\n",
    "    \n",
    "    # Process all Java files\n",
    "    for root, dirs, files in os.walk(project_path):\n",
    "        for file in files:\n",
    "            if file.endswith('.java'):\n",
    "                file_path = os.path.join(root, file)\n",
    "                rel_path = os.path.relpath(file_path, start='.')\n",
    "                \n",
    "                try:\n",
    "                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:\n",
    "                        code = f.read()\n",
    "                    \n",
    "                    stats['files_processed'] += 1\n",
    "                    print(f\"📄 Processing: {rel_path}\")\n",
    "                    \n",
    "                    # Parse Java code\n",
    "                    try:\n",
    "                        tree = javalang.parse.parse(code)\n",
    "                    except Exception as e:\n",
    "                        print(f\"   ⚠️ Parse error: {e}\")\n",
    "                        continue\n",
    "                    \n",
    "                    # Extract Spring annotations\n",
    "                    annotations = extract_spring_annotations(code)\n",
    "                    \n",
    "                    # Extract API endpoints\n",
    "                    endpoints = extract_api_endpoints(code)\n",
    "                    stats['endpoints_found'] += len(endpoints)\n",
    "                    \n",
    "                    # Extract database operations\n",
    "                    db_ops = extract_database_operations(code)\n",
    "                    tables.update(db_ops['tables'])\n",
    "                    stats['database_operations'] += len(db_ops['reads']) + len(db_ops['writes']) + len(db_ops['deletes'])\n",
    "                    \n",
    "                    # Enhanced database and table detection\n",
    "                    file_databases, file_tables = extract_database_and_tables(\n",
    "                        code, file_path, nodes, relations, existing_relations\n",
    "                    )\n",
    "                    databases.update(file_databases)\n",
    "                    tables.update(file_tables)\n",
    "                    \n",
    "                    # Analyze database usage patterns\n",
    "                    db_usage_ops = analyze_database_usage(\n",
    "                        code, file_path, nodes, relations, existing_relations\n",
    "                    )\n",
    "                    stats['database_operations'] += len(db_usage_ops)"\n",
    "                    \n",
    "                    # Register file node\n",
    "                    file_node = f\"file:{rel_path}\"\n",
    "                    register_node(nodes, file_node, rel_path)\n",
    "                    \n",
    "                    # Process classes, interfaces, and enums\n",
    "                    for path, class_decl in tree.filter(javalang.tree.ClassDeclaration):\n",
    "                        stats['classes_found'] += 1\n",
    "                        full_class_name = f\"{tree.package.name}.{class_decl.name}\" if tree.package else class_decl.name\n",
    "                        class_node = f\"class:{full_class_name}\"\n",
    "                        \n",
    "                        # Register class and connect to file\n",
    "                        register_node(nodes, class_node, rel_path)\n",
    "                        add_relation(relations, existing_relations, file_node, \"CONTAINS\", class_node, rel_path, nodes)\n",
    "                        \n",
    "                        # Handle inheritance\n",
    "                        if hasattr(class_decl, 'extends') and class_decl.extends:\n",
    "                            parent_class = f\"class:{class_decl.extends.name}\"\n",
    "                            add_relation(relations, existing_relations, class_node, \"EXTENDS\", parent_class, rel_path, nodes)\n",
    "                        \n",
    "                        # Handle interfaces\n",
    "                        if hasattr(class_decl, 'implements') and class_decl.implements:\n",
    "                            for interface in class_decl.implements:\n",
    "                                interface_node = f\"interface:{interface.name}\"\n",
    "                                add_relation(relations, existing_relations, class_node, \"IMPLEMENTS\", interface_node, rel_path, nodes)\n",
    "                    \n",
    "                    # Process interfaces\n",
    "                    for path, interface_decl in tree.filter(javalang.tree.InterfaceDeclaration):\n",
    "                        stats['classes_found'] += 1  # Count interfaces as classes for stats\n",
    "                        full_interface_name = f\"{tree.package.name}.{interface_decl.name}\" if tree.package else interface_decl.name\n",
    "                        interface_node = f\"interface:{full_interface_name}\"\n",
    "                        \n",
    "                        # Register interface and connect to file\n",
    "                        register_node(nodes, interface_node, rel_path)\n",
    "                        add_relation(relations, existing_relations, file_node, \"CONTAINS\", interface_node, rel_path, nodes)\n",
    "                        \n",
    "                        # Handle interface inheritance\n",
    "                        if hasattr(interface_decl, 'extends') and interface_decl.extends:\n",
    "                            for parent_interface in interface_decl.extends:\n",
    "                                parent_interface_node = f\"interface:{parent_interface.name}\"\n",
    "                                add_relation(relations, existing_relations, interface_node, \"EXTENDS\", parent_interface_node, rel_path, nodes)\n",
    "                    \n",
    "                    # Process enums\n",
    "                    for path, enum_decl in tree.filter(javalang.tree.EnumDeclaration):\n",
    "                        stats['classes_found'] += 1  # Count enums as classes for stats\n",
    "                        full_enum_name = f\"{tree.package.name}.{enum_decl.name}\" if tree.package else enum_decl.name\n",
    "                        enum_node = f\"enum:{full_enum_name}\"\n",
    "                        \n",
    "                        # Register enum and connect to file\n",
    "                        register_node(nodes, enum_node, rel_path)\n",
    "                        add_relation(relations, existing_relations, file_node, \"CONTAINS\", enum_node, rel_path, nodes)"\n",
    "                        \n",
    "                        # Process methods\n",
    "                        for method_path, method in class_decl.filter(javalang.tree.MethodDeclaration):\n",
    "                            stats['methods_found'] += 1\n",
    "                            method_node = f\"method:{full_class_name}.{method.name}\"\n",
    "                            \n",
    "                            # Register method and connect to class\n",
    "                            register_node(nodes, method_node, rel_path)\n",
    "                            add_relation(relations, existing_relations, class_node, \"HAS_METHOD\", method_node, rel_path, nodes)\n",
    "                            \n",
    "                            # Check if method is an API endpoint\n",
    "                            for endpoint in endpoints:\n",
    "                                # Simple heuristic: if endpoint annotation is near method\n",
    "                                endpoint_tracker.register_endpoint(\n",
    "                                    endpoint['path'], method, endpoint['http_method'],\n",
    "                                    full_class_name, method.name, rel_path\n",
    "                                )\n",
    "                            \n",
    "                            # Extract variable transformations\n",
    "                            if hasattr(method, 'body') and method.body:\n",
    "                                method_body = [(path, node) for path, node in method.body]\n",
    "                                \n",
    "                                # Variable transformations\n",
    "                                extract_variable_transformations(\n",
    "                                    method_body, full_class_name, method.name,\n",
    "                                    relations, existing_relations, nodes,\n",
    "                                    variable_flows, method_assignments, rel_path\n",
    "                                )\n",
    "                                \n",
    "                                # Control flow structures\n",
    "                                cf_count = extract_control_flow_structures(\n",
    "                                    method_body, full_class_name, method.name,\n",
    "                                    relations, existing_relations, nodes,\n",
    "                                    variable_flows, method_assignments, rel_path\n",
    "                                )\n",
    "                                stats['control_flow_structures'] += cf_count\n",
    "                    \n",
    "                except Exception as e:\n",
    "                    print(f\"   ❌ Error processing {rel_path}: {e}\")\n",
    "                    continue\n",
    "    \n",
    "    # Create database and table nodes\n",
    "    for table in tables:\n",
    "        table_node = f\"table:{table}\"\n",
    "        register_node(nodes, table_node, \"database\")\n",
    "    \n",
    "    stats['variable_transformations'] = len(variable_flows)\n",
    "    \n",
    "    # Prepare final data structure\n",
    "    graph_data = {\n",
    "        'nodes': nodes,\n",
    "        'relations': relations,\n",
    "        'variable_flows': variable_flows,\n",
    "        'endpoint_tracker': endpoint_tracker,\n",
    "        'databases': list(databases),\n",
    "        'tables': list(tables),\n",
    "        'statistics': stats,\n",
    "        'endpoint_summary': {\n",
    "            'total_endpoints': len(endpoint_tracker.endpoints),\n",
    "            'unique_paths': len(set(ep['path'] for ep in endpoint_tracker.endpoints.values())),\n",
    "            'method_mappings': len(endpoint_tracker.endpoint_methods)\n",
    "        },\n",
    "        'transformation_summary': {\n",
    "            'variable_flows': len(variable_flows),\n",
    "            'control_flow_structures': stats['control_flow_structures']\n",
    "        }\n",
    "    }\n",
    "    \n",
    "    print(f\"\\n✅ Analysis completed!\")\n",
    "    print(f\"📊 Statistics:\")\n",
    "    for key, value in stats.items():\n",
    "        print(f\"   - {key.replace('_', ' ').title()}: {value:,}\")\n",
    "    \n",
    "    return graph_data\n",
    "\n",
    "print(\"✅ Main extraction function loaded\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "export-functions-header",
   "metadata": {},
   "source": [
    "## 7. Export Functions"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "export-functions",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ================== EXPORT FUNCTIONS ==================\n",
    "\n",
    "def save_to_csv(graph_data, output_dir=\"enhanced_graph_csv_v2\"):\n",
    "    \"\"\"Save graph data to CSV files.\"\"\"\n",
    "    print(f\"💾 Saving graph data to CSV files in: {output_dir}\")\n",
    "    \n",
    "    os.makedirs(output_dir, exist_ok=True)\n",
    "    \n",
    "    # Save nodes\n",
    "    nodes_data = []\n",
    "    for node_id, node_info in graph_data['nodes'].items():\n",
    "        nodes_data.append({\n",
    "            'id': node_id,\n",
    "            'type': node_info['type'],\n",
    "            'name': node_info['name'],\n",
    "            'full_name': node_info['full_name'],\n",
    "            'file_path': node_info['file_path']\n",
    "        })\n",
    "    \n",
    "    nodes_df = pd.DataFrame(nodes_data)\n",
    "    nodes_file = os.path.join(output_dir, \"nodes.csv\")\n",
    "    nodes_df.to_csv(nodes_file, index=False)\n",
    "    print(f\"   ✅ Nodes saved: {len(nodes_data)} records -> {nodes_file}\")\n",
    "    \n",
    "    # Save relations\n",
    "    relations_df = pd.DataFrame(graph_data['relations'])\n",
    "    relations_file = os.path.join(output_dir, \"relations.csv\")\n",
    "    relations_df.to_csv(relations_file, index=False)\n",
    "    print(f\"   ✅ Relations saved: {len(graph_data['relations'])} records -> {relations_file}\")\n",
    "    \n",
    "    # Save variable flows\n",
    "    if graph_data['variable_flows']:\n",
    "        flows_df = pd.DataFrame(graph_data['variable_flows'])\n",
    "        flows_file = os.path.join(output_dir, \"variable_flows.csv\")\n",
    "        flows_df.to_csv(flows_file, index=False)\n",
    "        print(f\"   ✅ Variable flows saved: {len(graph_data['variable_flows'])} records -> {flows_file}\")\n",
    "    \n",
    "    # Save endpoints\n",
    "    if graph_data['endpoint_tracker'].endpoints:\n",
    "        endpoints_data = []\n",
    "        for endpoint_key, endpoint_info in graph_data['endpoint_tracker'].endpoints.items():\n",
    "            endpoints_data.append({\n",
    "                'endpoint_key': endpoint_key,\n",
    "                'path': endpoint_info['path'],\n",
    "                'http_method': endpoint_info['http_method'],\n",
    "                'class_name': endpoint_info['class_name'],\n",
    "                'method_name': endpoint_info['method_name'],\n",
    "                'file_path': endpoint_info['file_path']\n",
    "            })\n",
    "        \n",
    "        endpoints_df = pd.DataFrame(endpoints_data)\n",
    "        endpoints_file = os.path.join(output_dir, \"endpoints.csv\")\n",
    "        endpoints_df.to_csv(endpoints_file, index=False)\n",
    "        print(f\"   ✅ Endpoints saved: {len(endpoints_data)} records -> {endpoints_file}\")\n",
    "    \n",
    "    # Save summary\n",
    "    summary_data = {\n",
    "        'total_nodes': len(graph_data['nodes']),\n",
    "        'total_relations': len(graph_data['relations']),\n",
    "        'total_endpoints': graph_data['endpoint_summary']['total_endpoints'],\n",
    "        'variable_flows': len(graph_data['variable_flows']),\n",
    "        'control_flow_structures': graph_data['transformation_summary']['control_flow_structures'],\n",
    "        'tables_found': len(graph_data['tables']),\n",
    "        'export_timestamp': datetime.now().isoformat()\n",
    "    }\n",
    "    \n",
    "    summary_file = os.path.join(output_dir, \"summary.json\")\n",
    "    with open(summary_file, 'w') as f:\n",
    "        json.dump(summary_data, f, indent=2)\n",
    "    print(f\"   ✅ Summary saved: {summary_file}\")\n",
    "    \n",
    "    return output_dir\n",
    "\n",
    "def push_to_neo4j(graph_data, uri, user, password, database=\"neo4j\"):\n",
    "    \"\"\"Push complete graph data to Neo4j - single unified function.\"\"\"\n",
    "    print(f\"🔗 Connecting to Neo4j at {uri}...\")\n",
    "    \n",
    "    try:\n",
    "        driver = GraphDatabase.driver(uri, auth=(user, password))\n",
    "        print(f\"✅ Connected to Neo4j successfully\")\n",
    "        \n",
    "        with driver.session(database=database) as session:\n",
    "            # Clear existing data\n",
    "            print(\"🧹 Clearing existing data...\")\n",
    "            session.run(\"MATCH (n) DETACH DELETE n\")\n",
    "            \n",
    "            # Create nodes in batches\n",
    "            print(\"📦 Creating nodes...\")\n",
    "            batch_size = 1000\n",
    "            node_count = 0\n",
    "            \n",
    "            nodes_list = list(graph_data['nodes'].values())\n",
    "            for i in range(0, len(nodes_list), batch_size):\n",
    "                batch = nodes_list[i:i + batch_size]\n",
    "                \n",
    "                # Create nodes with dynamic labels\n",
    "                for node in batch:\n",
    "                    label = node['type'].title().replace('_', '')\n",
    "                    query = f\"\"\"\n",
    "                    CREATE (n:{label} {{\n",
    "                        id: $id,\n",
    "                        name: $name,\n",
    "                        full_name: $full_name,\n",
    "                        file_path: $file_path\n",
    "                    }})\n",
    "                    \"\"\"\n",
    "                    session.run(query, **node)\n",
    "                    node_count += 1\n",
    "                \n",
    "                if (i // batch_size + 1) % 5 == 0:\n",
    "                    print(f\"   Processed {node_count} nodes...\")\n",
    "            \n",
    "            # Create relationships in batches\n",
    "            print(\"🔗 Creating relationships...\")\n",
    "            rel_count = 0\n",
    "            \n",
    "            for i in range(0, len(graph_data['relations']), batch_size):\n",
    "                batch = graph_data['relations'][i:i + batch_size]\n",
    "                \n",
    "                for rel in batch:\n",
    "                    query = \"\"\"\n",
    "                    MATCH (a {id: $source}), (b {id: $target})\n",
    "                    CREATE (a)-[r:RELATION {type: $relation, file_path: $file_path}]->(b)\n",
    "                    \"\"\"\n",
    "                    session.run(query, **rel)\n",
    "                    rel_count += 1\n",
    "                \n",
    "                if (i // batch_size + 1) % 5 == 0:\n",
    "                    print(f\"   Processed {rel_count} relationships...\")\n",
    "            \n",
    "            print(f\"\\n✅ Successfully pushed to Neo4j:\")\n",
    "            print(f\"   - {node_count} nodes\")\n",
    "            print(f\"   - {rel_count} relationships\")\n",
    "            print(f\"   - {graph_data['endpoint_summary']['total_endpoints']} endpoints tracked\")\n",
    "            print(f\"   - {len(graph_data['variable_flows'])} variable flows\")\n",
    "            print(f\"   - {graph_data['transformation_summary']['control_flow_structures']} control flow structures\")\n",
    "    \n",
    "    except Exception as e:\n",
    "        print(f\"❌ Error connecting to Neo4j: {e}\")\n",
    "        raise\n",
    "    finally:\n",
    "        if 'driver' in locals():\n",
    "            driver.close()\n",
    "\n",
    "print(\"✅ Export functions loaded\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "execution-header",
   "metadata": {},
   "source": [
    "## 8. Execute Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "main-execution",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ================== MAIN EXECUTION ==================\n",
    "\n",
    "# Check if project path exists\n",
    "if not os.path.exists(PROJECT_PATH):\n",
    "    print(f\"❌ Project path does not exist: {PROJECT_PATH}\")\n",
    "    print(\"Please update PROJECT_PATH in the configuration section.\")\n",
    "    graph_data = None\n",
    "else:\n",
    "    print(f\"🚀 Starting comprehensive Java code analysis...\")\n",
    "    print(f\"📁 Processing project: {PROJECT_PATH}\")\n",
    "    \n",
    "    try:\n",
    "        # Run complete analysis\n",
    "        graph_data = extract_complete_java_analysis(PROJECT_PATH)\n",
    "        \n",
    "        print(f\"\\n🎉 Analysis completed successfully!\")\n",
    "        print(f\"📊 Final Results:\")\n",
    "        print(f\"   - Total nodes: {len(graph_data['nodes']):,}\")\n",
    "        print(f\"   - Total relations: {len(graph_data['relations']):,}\")\n",
    "        print(f\"   - API endpoints: {graph_data['endpoint_summary']['total_endpoints']:,}\")\n",
    "        print(f\"   - Variable flows: {len(graph_data['variable_flows']):,}\")\n",
    "        print(f\"   - Control flow structures: {graph_data['transformation_summary']['control_flow_structures']:,}\")\n",
    "        print(f\"   - Database tables: {len(graph_data['tables']):,}\")\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f\"❌ Error during analysis: {e}\")\n",
    "        import traceback\n",
    "        traceback.print_exc()\n",
    "        graph_data = None"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "csv-export-header",
   "metadata": {},
   "source": [
    "## 9. Export to CSV"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "csv-export",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ================== CSV EXPORT ==================\n",
    "\n",
    "if 'graph_data' in locals() and graph_data is not None:\n",
    "    try:\n",
    "        print(f\"\\n💾 Exporting to CSV files...\")\n",
    "        csv_output_path = save_to_csv(graph_data, output_dir=CSV_OUTPUT_DIR)\n",
    "        \n",
    "        print(f\"\\n📁 CSV files saved successfully!\")\n",
    "        print(f\"📂 Location: {csv_output_path}\")\n",
    "        \n",
    "        # Show sample data\n",
    "        nodes_file = os.path.join(csv_output_path, \"nodes.csv\")\n",
    "        if os.path.exists(nodes_file):\n",
    "            sample_nodes = pd.read_csv(nodes_file).head(3)\n",
    "            print(f\"\\n📋 Sample nodes data:\")\n",
    "            print(sample_nodes[['type', 'name', 'full_name']].to_string(index=False))\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f\"❌ Error saving CSV files: {e}\")\n",
    "        import traceback\n",
    "        traceback.print_exc()\n",
    "else:\n",
    "    print(\"⚠️ No graph data available to export. Please run the analysis first.\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "neo4j-export-header",
   "metadata": {},
   "source": [
    "## 10. Push to Neo4j (Final Step)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "neo4j-export",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ================== NEO4J EXPORT (SINGLE PUSH) ==================\n",
    "\n",
    "if 'graph_data' in locals() and graph_data is not None:\n",
    "    try:\n",
    "        print(f\"\\n🔗 Pushing results to Neo4j...\")\n",
    "        print(f\"⚠️ This will clear existing data in the Neo4j database!\")\n",
    "        \n",
    "        # Uncomment the next line to actually push to Neo4j\n",
    "        # push_to_neo4j(graph_data, NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD, NEO4J_DB)\n",
    "        \n",
    "        print(f\"\\n💡 To push to Neo4j, uncomment the push_to_neo4j() call above\")\n",
    "        print(f\"🔍 Neo4j Browser: http://localhost:7474\")\n",
    "        print(f\"\\n🎯 Sample Neo4j queries to try after pushing:\")\n",
    "        print(f\"   • MATCH (n) RETURN labels(n), count(n) - Count nodes by type\")\n",
    "        print(f\"   • MATCH (c:Class)-[:RELATION {{type: 'HAS_METHOD'}}]->(m:Method) RETURN c.name, count(m)\")\n",
    "        print(f\"   • MATCH (v:Variable)-[:RELATION {{type: 'TRANSFORMS_TO'}}]->(o:Operation) RETURN v.name, o.name\")\n",
    "        print(f\"   • MATCH (m:Method)-[:RELATION {{type: 'HAS_CONDITION'}}]->(c:Condition) RETURN m.name, c.name\")\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f\"❌ Error with Neo4j setup: {e}\")\n",
    "        print(f\"💡 Make sure Neo4j is running and credentials are correct\")\n",
    "else:\n",
    "    print(\"⚠️ No graph data available to push. Please run the analysis first.\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "summary-header",
   "metadata": {},
   "source": [
    "## 11. Final Summary"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "final-summary",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ================== FINAL SUMMARY ==================\n",
    "\n",
    "if 'graph_data' in locals() and graph_data is not None:\n",
    "    print(\"\\n🎉 JAVA CODE ANALYSIS COMPLETE! 🎉\")\n",
    "    print(\"=\" * 60)\n",
    "    print(f\"📊 FINAL STATISTICS:\")\n",
    "    print(f\"   • Files processed: {graph_data['statistics']['files_processed']:,}\")\n",
    "    print(f\"   • Classes found: {graph_data['statistics']['classes_found']:,}\")\n",
    "    print(f\"   • Methods found: {graph_data['statistics']['methods_found']:,}\")\n",
    "    print(f\"   • Total nodes: {len(graph_data['nodes']):,}\")\n",
    "    print(f\"   • Total relationships: {len(graph_data['relations']):,}\")\n",
    "    print(f\"   • API endpoints: {graph_data['endpoint_summary']['total_endpoints']:,}\")\n",
    "    print(f\"   • Variable transformations: {len(graph_data['variable_flows']):,}\")\n",
    "    print(f\"   • Control flow structures: {graph_data['transformation_summary']['control_flow_structures']:,}\")\n",
    "    print(f\"   • Database tables: {len(graph_data['tables']):,}\")\n",
    "    \n",
    "    print(f\"\\n📁 OUTPUT LOCATIONS:\")\n",
    "    print(f\"   • CSV files: ./{CSV_OUTPUT_DIR}/\")\n",
    "    print(f\"   • Neo4j database: {NEO4J_DB} (when pushed)\")\n",
    "    \n",
    "    print(f\"\\n🔍 KEY IMPROVEMENTS IN V2:\")\n",
    "    print(f\"   ✅ Removed duplicate functions\")\n",
    "    print(f\"   ✅ Single Neo4j push at the end\")\n",
    "    print(f\"   ✅ Clean execution flow\")\n",
    "    print(f\"   ✅ Enhanced data lineage with transformation chains\")\n",
    "    print(f\"   ✅ Consolidated similar functions\")\n",
    "    print(f\"   ✅ Better error handling and logging\")\n",
    "    \n",
    "    print(f\"\\n🚀 WHAT YOU CAN DO NEXT:\")\n",
    "    print(f\"   1. Explore CSV files for detailed data analysis\")\n",
    "    print(f\"   2. Push to Neo4j for interactive graph exploration\")\n",
    "    print(f\"   3. Use variable flows for data lineage analysis\")\n",
    "    print(f\"   4. Analyze control flow structures for code complexity\")\n",
    "    print(f\"   5. Track API endpoint usage and dependencies\")\n",
    "    \n",
    "    print(f\"\\n✨ Happy analyzing with the clean v2 pipeline! ✨\")\n",
    "else:\n",
    "    print(\"\\n⚠️ Analysis was not completed successfully.\")\n",
    "    print(\"Please check the error messages above and try again.\")\n",
    "    print(\"\\n🔧 Common issues:\")\n",
    "    print(\"   - Check PROJECT_PATH configuration\")\n",
    "    print(\"   - Ensure Java files exist in the project\")\n",
    "    print(\"   - Verify file permissions\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "venv",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.11.8"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
