path,http_method,defined_in_class,defined_in_method,file_path,method_id
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/almconfig"")",GET,com.bolt.dashboard.api.ALMConfigController,saveALMConfig,OneInsights\ServicesBolt\api\ALMConfigController.java,method:com.bolt.dashboard.api.ALMConfigController.saveALMConfig
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/almconfigDetails"")",GET,com.bolt.dashboard.api.ALMConfigController,retrieveList,OneInsights\ServicesBolt\api\ALMConfigController.java,method:com.bolt.dashboard.api.ALMConfigController.retrieveList
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/almconfigDetailsConfig"")",GET,com.bolt.dashboard.api.ALMConfigController,retrieveAlmConfig,OneInsights\ServicesBolt\api\ALMConfigController.java,method:com.bolt.dashboard.api.ALMConfigController.retrieveAlmConfig
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/storyAgeing"")",GET,com.bolt.dashboard.api.AlmController,storyAgeing,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.storyAgeing
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/groomingTable"")",GET,com.bolt.dashboard.api.AlmController,groomingTable,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.groomingTable
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/delDuplicate"")",GET,com.bolt.dashboard.api.AlmController,delDuplicate,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.delDuplicate
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/sprintProgressHome"")",GET,com.bolt.dashboard.api.AlmController,getSprintProgressHome,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getSprintProgressHome
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/defectInsightData"")",GET,com.bolt.dashboard.api.AlmController,defectInsightData,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.defectInsightData
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/defectTrendAndClassification"")",GET,com.bolt.dashboard.api.AlmController,defectTrendAndClassification,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.defectTrendAndClassification
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/defectClassification"")",GET,com.bolt.dashboard.api.AlmController,defectClassification,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.defectClassification
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/issueBrakeUp"")",GET,com.bolt.dashboard.api.AlmController,getIssueBrakeUp,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getIssueBrakeUp
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getStoryProgress"")",GET,com.bolt.dashboard.api.AlmController,getStoryProgress,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getStoryProgress
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/defectsSummaryHome"")",GET,com.bolt.dashboard.api.AlmController,getDefectsSummaryHome,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getDefectsSummaryHome
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/taskRisk"")",GET,com.bolt.dashboard.api.AlmController,getTaskRiskStoryPoint,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getTaskRiskStoryPoint
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/burndown"")",GET,com.bolt.dashboard.api.AlmController,burndownCalculation,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.burndownCalculation
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getProductionSlippage"")",GET,com.bolt.dashboard.api.AlmController,getProductionSlippage,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getProductionSlippage
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getDefectDensity"")",GET,com.bolt.dashboard.api.AlmController,getDefectDensity,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getDefectDensity
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getDefectBacklog"")",GET,com.bolt.dashboard.api.AlmController,getDefectBacklog,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getDefectBacklog
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getDefectPareto"")",GET,com.bolt.dashboard.api.AlmController,getDefectPareto,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getDefectPareto
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/activeSprints"")",GET,com.bolt.dashboard.api.AlmController,getActiveSprints,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getActiveSprints
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/delAllIssues"")",GET,com.bolt.dashboard.api.AlmController,delAllIsues,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.delAllIsues
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getMetric"")",GET,com.bolt.dashboard.api.AlmController,getMetricsDatas,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getMetricsDatas
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getAllTransitions"")",GET,com.bolt.dashboard.api.AlmController,getAllTransitions,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getAllTransitions
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getProjectMetrics"")",GET,com.bolt.dashboard.api.AlmController,getProjectMetrics,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getProjectMetrics
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getChangeItems"")",GET,com.bolt.dashboard.api.AlmController,getChangesItems,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getChangesItems
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getTransitions"")",GET,com.bolt.dashboard.api.AlmController,getTransitionsData,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getTransitionsData
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getIteration"")",GET,com.bolt.dashboard.api.AlmController,getIterationData,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getIterationData
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getEfort"")",GET,com.bolt.dashboard.api.AlmController,getEffortData,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getEffortData
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getProjectDetails"")",GET,com.bolt.dashboard.api.AlmController,getProjectDetials,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getProjectDetials
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getCurrentProjectDetails"")",GET,com.bolt.dashboard.api.AlmController,getCurrentProjectDetials,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getCurrentProjectDetials
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getCurrentIter"")",GET,com.bolt.dashboard.api.AlmController,getCurrentIter,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getCurrentIter
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getIterations"")",GET,com.bolt.dashboard.api.AlmController,getIterations,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getIterations
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getDefectCount"")",GET,com.bolt.dashboard.api.AlmController,getDefectCount,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getDefectCount
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getRelease"")",GET,com.bolt.dashboard.api.AlmController,getRelease,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getRelease
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getUnReleaseData"")",GET,com.bolt.dashboard.api.AlmController,getUnReleaseData,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getUnReleaseData
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getDefects"")",GET,com.bolt.dashboard.api.AlmController,getDefects,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getDefects
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/slaData"")",GET,com.bolt.dashboard.api.AlmController,getSlaData,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getSlaData
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/assigneeAlm"")",GET,com.bolt.dashboard.api.AlmController,getAssigneeIssues,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getAssigneeIssues
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/dateIteration"")",GET,com.bolt.dashboard.api.AlmController,getDateIterations,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getDateIterations
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getProdDefects"")",GET,com.bolt.dashboard.api.AlmController,getProdDefects,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getProdDefects
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getAlmType"")",GET,com.bolt.dashboard.api.AlmController,getAlmType,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getAlmType
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getVelocityChart"")",GET,com.bolt.dashboard.api.AlmController,getVelocityChart,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getVelocityChart
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getIssueHierarchy"")",GET,com.bolt.dashboard.api.AlmController,getIssueHierarchy,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getIssueHierarchy
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getComponentWiseIssueHierarchy"")",GET,com.bolt.dashboard.api.AlmController,getComponentWiseIssueHierarchy,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getComponentWiseIssueHierarchy
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getComponentWiseVelocityChart"")",GET,com.bolt.dashboard.api.AlmController,getComponentWiseVelocityChart,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getComponentWiseVelocityChart
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getComponontWiseSprintWiseStories"")",GET,com.bolt.dashboard.api.AlmController,getComponontWiseSprintWiseStories,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getComponontWiseSprintWiseStories
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getComponents"")",GET,com.bolt.dashboard.api.AlmController,getComponents,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getComponents
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/updateComponent"")",GET,com.bolt.dashboard.api.AlmController,updateComponent,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.updateComponent
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/saveEngScore"")",GET,com.bolt.dashboard.api.AlmController,saveEngScore,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.saveEngScore
"Literal(postfix_operators=[], prefix_operators=[], qualifier=None, selectors=[], value=""/getFeatureMetrics"")",GET,com.bolt.dashboard.api.AlmController,getFeatureMetrics,OneInsights\ServicesBolt\api\AlmController.java,method:com.bolt.dashboard.api.AlmController.getFeatureMetrics
